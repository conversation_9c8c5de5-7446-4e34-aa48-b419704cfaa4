package com.example.service;

import com.example.mapper.UserMapper;
import com.example.model.User;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class AuthService {

    @Resource
    private UserMapper userMapper;

    public User login(String email, String password) {
        User user = userMapper.findByUserAccount(email);
        if (user == null || !Objects.equals(user.getUserPassword(), password)) {
            throw new RuntimeException("账号或密码错误");
        }
        return user;
    }

    public void register(User user) {
        if (userMapper.findByUserAccount(user.getUserAccount()) != null) {
            throw new RuntimeException("账号已被注册");
        }
        userMapper.insert(user);
    }
}
