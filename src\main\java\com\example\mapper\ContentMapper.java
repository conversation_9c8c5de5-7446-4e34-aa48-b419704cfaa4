
package com.example.mapper;

import com.example.model.ContentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContentMapper {

    List<ContentVO> selectFeatured();

    List<ContentVO> selectHotRanking();

    List<ContentVO> selectLatestCourse();

    List<ContentVO> selectRecommendVideo(Long userId);
    List<ContentVO> selectLatestVideo();

    // ✅ 新增：根据标签 ID 列表查询推荐内容
    List<ContentVO> selectContentByTagIds(@Param("list") List<Long> tagIds);
    List<ContentVO> selectContentByProfileAttributes(
            @Param("topic") String dominantTopic,
            @Param("style") String learningStyle,
            @Param("career") String careerTendency
    );


}
