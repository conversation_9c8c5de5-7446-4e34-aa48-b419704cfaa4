package com.example.service;

import com.example.mapper.ContentMapper;
import com.example.dto.HomePageResponse;
import com.example.model.ContentVO;
import com.example.model.UserProfileAi;
import com.example.mapper.UserProfileAiMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class HomeServiceImpl implements HomeService {

    @Autowired
    private ContentMapper contentMapper;

    @Autowired
    private UserProfileAiMapper userProfileAiMapper;

    @Override
    public HomePageResponse getHomePageContent(Long userId) {
        HomePageResponse response = new HomePageResponse();

        // 精品内容
        List<ContentVO> featured = contentMapper.selectFeatured();
        response.setFeaturedContentList(featured);

        // 热门课程
        List<ContentVO> hot = contentMapper.selectHotRanking();
        response.setHotCourseList(hot);

        // 最新课程
        List<ContentVO> latest = contentMapper.selectLatestCourse();
        response.setLatestCourseList(latest);

        if (userId == null) {
            System.out.println("✅ 游客访问首页，返回默认推荐内容");

            // 可选推荐源：精选内容 / 热门课程 / 混合
            List<ContentVO> defaultInterest = contentMapper.selectFeatured();
            List<ContentVO> defaultProfile = contentMapper.selectHotRanking();

            response.setInterestRecommend(defaultInterest != null ? defaultInterest : Collections.emptyList());
            response.setProfileRecommend(defaultProfile != null ? defaultProfile : Collections.emptyList());

            return response;
        }


        // 有 userId，则走个性化逻辑
        UserProfileAi profile = userProfileAiMapper.selectByUserId(userId);
        if (profile != null) {
            // 兴趣推荐
            if (profile.getPreferredContentTagIds() != null && !profile.getPreferredContentTagIds().isEmpty()) {
                List<Long> tagIdList = Arrays.stream(profile.getPreferredContentTagIds().split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                List<ContentVO> interestList = contentMapper.selectContentByTagIds(tagIdList);
                response.setInterestRecommend(interestList);
            } else {
                response.setInterestRecommend(Collections.emptyList());
            }

            // 多维画像推荐
            List<ContentVO> profileMatched = contentMapper.selectContentByProfileAttributes(
                    profile.getDominantTopic(),
                    profile.getLearningStyle(),
                    profile.getCareerTendency()
            );
            response.setProfileRecommend(profileMatched);
        } else {
            response.setInterestRecommend(Collections.emptyList());
            response.setProfileRecommend(Collections.emptyList());
        }

        return response;
    }


}
