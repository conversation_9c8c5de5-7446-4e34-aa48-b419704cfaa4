package com.example.controller;

import com.example.dto.SaveTagRequest;
import com.example.dto.UserProfileAiResponse;
import com.example.service.UserProfileService;
import com.example.utils.BaseResponse;
import com.example.utils.JwtUtil;
import com.example.utils.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.example.model.User;
import com.example.mapper.UserMapper;

@RestController
@RequestMapping("/api/profile")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;
    @Autowired
    private UserMapper userMapper;

    /**
     * 保存用户选择的标签，并生成AI画像（伪造数据）
     */
    @PostMapping("/save")
    public BaseResponse<UserProfileAiResponse> saveUserTagAndGenerateProfile(
            @RequestBody SaveTagRequest request,
            HttpServletRequest httpServletRequest
    ) {
        // ✅ 从 token 提取 userAccount
        String userAccount = JwtUtil.getUserAccountFromRequest(httpServletRequest);

        // ✅ 查询数据库获取 userId
        User user = userMapper.findByUserAccount(userAccount);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        Long userId = user.getId();
        request.setUserId(userId);

        // ✅ 保存标签 + 生成模拟画像
        userProfileService.saveUserTag(request);
        UserProfileAiResponse aiProfile = userProfileService.generateFakeAiProfile(userId);
        // ✅ 打印调试：推荐标签是否已更新
        System.out.println("✅ 当前用户画像推荐标签 = " + aiProfile.getPreferredContentTagIds());
        return ResultUtils.success(aiProfile);
    }

    /**
     * 获取当前用户画像
     */
    @GetMapping("/ai")
    public BaseResponse<UserProfileAiResponse> getAiProfile(HttpServletRequest req) {
        String userAccount = JwtUtil.getUserAccountFromRequest(req);
        User user = userMapper.findByUserAccount(userAccount);

        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        Long userId = user.getId();
        return ResultUtils.success(userProfileService.getUserProfile(userId));
    }

}
