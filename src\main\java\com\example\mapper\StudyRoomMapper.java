package com.example.mapper;

import com.example.model.StudyRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StudyRoomMapper {

    // 插入新自习室（返回主键）
    int insert(StudyRoom studyRoom);

    // 根据 ID 查询房间
    StudyRoom selectById(@Param("id") Long id);

    // 查询我创建的房间
    List<StudyRoom> selectByOwnerId(@Param("ownerId") Long ownerId);

    // 查询所有公开房间（用于首页推荐）
    List<StudyRoom> selectPublicRooms();

    // 根据标签筛选公开房间
    List<StudyRoom> selectPublicRoomsByTag(@Param("tag") String tag);

    // 更新房间信息
    int updateRoom(StudyRoom studyRoom);

    // 删除房间（只有房主可以删）
    int deleteById(@Param("id") Long id);
}
