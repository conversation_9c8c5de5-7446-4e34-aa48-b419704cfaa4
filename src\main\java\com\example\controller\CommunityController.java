package com.example.controller;

import com.example.dto.CommunityPostQueryRequest;
import com.example.model.CommunityPostVO;
import com.example.service.CommunityService;
import com.example.utils.BaseResponse;
import com.example.utils.ResultUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/community")
public class CommunityController {

    @Autowired
    private CommunityService communityService;

    @GetMapping("/list")
    public BaseResponse<List<CommunityPostVO>> listPosts(@Valid CommunityPostQueryRequest request) {
        List<CommunityPostVO> posts = communityService.listCommunityPosts(request);
        return ResultUtils.success(posts);
    }
}
