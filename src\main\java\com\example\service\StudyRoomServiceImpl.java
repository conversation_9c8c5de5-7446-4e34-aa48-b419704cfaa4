package com.example.service;

import com.example.dto.CreateRoomRequest;
import com.example.mapper.StudyRoomMapper;
import com.example.mapper.StudyRoomMemberMapper;
import com.example.model.StudyRoom;
import com.example.model.StudyRoomMember;
import com.example.service.StudyRoomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StudyRoomServiceImpl implements StudyRoomService {

    @Autowired
    private StudyRoomMapper studyRoomMapper;

    @Autowired
    private StudyRoomMemberMapper memberMapper;

    @Override
    public boolean createRoom(Long userId, CreateRoomRequest request) {
        StudyRoom room = new StudyRoom();
        room.setRoomName(request.getRoomName());
        room.setOwnerId(userId);
        room.setIsPublic(request.getIsPublic());
        room.setMaxMembers(request.getMaxMembers());
        room.setImageIndex(request.getImageIndex());
        room.setTag(request.getTag());
        room.setCanUseTools(request.getCanUseTools());

        int inserted = studyRoomMapper.insert(room);
        if (inserted <= 0 || room.getId() == null) {
            return false;
        }

        StudyRoomMember member = new StudyRoomMember();
        member.setRoomId(room.getId());
        member.setUserId(userId);
        member.setStatus(1);

        return memberMapper.insert(member) > 0;
    }
}
