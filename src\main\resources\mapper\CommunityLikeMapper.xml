<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.CommunityLikeMapper">

    <insert id="insertLike" parameterType="com.example.model.CommunityLike" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO communityLike (postId, userId, createTime)
        VALUES (#{postId}, #{userId}, #{createTime})
    </insert>

    <delete id="deleteLike">
        DELETE FROM communityLike
        WHERE postId = #{postId} AND userId = #{userId}
    </delete>

    <select id="exists" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM communityLike
        WHERE postId = #{postId} AND userId = #{userId}
    </select>
</mapper>