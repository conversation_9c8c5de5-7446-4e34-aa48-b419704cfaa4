package com.example.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.http.HttpServletRequest;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;

public class JwtUtil {

    private static final long EXPIRATION_TIME = ********; // 1 天
    private static final String SECRET = "study-partner-app-backend-key@2025"; // 🔒 自定义密钥
    private static final Key key = Keys.hmacShaKeyFor(SECRET.getBytes(StandardCharsets.UTF_8));

    /**
     * ✅ 生成 Token，存储 userAccount（作为 Subject）
     */
    public static String generateToken(String userAccount) {
        return Jwts.builder()
                .setSubject(userAccount) // Subject = userAccount
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(key)
                .compact();
    }

    /**
     * ✅ 从 Token 中解析出 userAccount
     */
    public static String parseTokenForAccount(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            String userAccount = claims.getSubject();
            System.out.println("✅ Token 解析成功，userAccount = " + userAccount);
            return userAccount;
        } catch (Exception e) {
            System.out.println("❌ Token 解析失败：" + e.getClass().getSimpleName() + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * ✅ 从请求头中提取 Bearer Token 并解析出 userAccount
     */
    public static String getUserAccountFromRequest(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            throw new IllegalArgumentException("Token 不存在");
        }
        if (token.startsWith("Bearer ")) {
            token = token.substring(7); // 去掉 "Bearer " 前缀
        }
        return parseTokenForAccount(token);
    }
    /**
     * ✅ 用于验证 token 是否合法（不关心返回值，只关心是否抛异常）
     */
    public static void validateToken(String token) {
        Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token); // ✅ 不抛异常就说明 token 合法
    }

}
