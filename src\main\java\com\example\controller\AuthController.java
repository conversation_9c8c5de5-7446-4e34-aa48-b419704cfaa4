package com.example.controller;

import com.example.dto.UserLoginResponse;
import com.example.mapper.UserMapper;
import com.example.model.User;
import com.example.utils.BaseResponse;
import com.example.utils.JwtUtil;
import com.example.utils.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private UserMapper repo;

    private final BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();

    @PostMapping("/login")
    public BaseResponse<UserLoginResponse> login(@RequestBody AuthRequest req) {
        User user = repo.findByUserAccount(req.getUserAccount());

        if (user == null || !encoder.matches(req.getUserPassword(), user.getUserPassword())) {
            return ResultUtils.error(401, "账号或密码错误");
        }

        // 生成 token
        String token = JwtUtil.generateToken(user.getUserAccount());

        // 封装返回
        UserLoginResponse resp = new UserLoginResponse();
        resp.setId(user.getId());
        resp.setUserAccount(user.getUserAccount());
        resp.setUserName(user.getUserName()); // 映射昵称
        resp.setUserAvatar(user.getUserAvatar());
        resp.setUserProfile("暂未填写"); // 可从数据库读取
        resp.setUserRole("user"); // 默认
        resp.setIsCompleted(false); // 默认 false
        resp.setToken(token);

        return ResultUtils.success(resp);
    }


    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody AuthRequest req) {
        // 先查重
        User existing = repo.findByUserAccount(req.userAccount);
        if (existing != null) {
            return ResponseEntity.status(400).body(Map.of("error", "账号已存在"));
        }

        // 保存用户（建议加密密码）
        User newUser = new User();
        newUser.setUserAccount(req.userAccount);
        newUser.setUserPassword(encoder.encode(req.userPassword)); // 使用加密
        repo.insert(newUser);

        return ResponseEntity.ok(Map.of("message", "注册成功"));
    }

    @GetMapping("/me")
    public BaseResponse<User> getCurrentUser(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");

        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return ResultUtils.error(401, "未提供有效 token");
        }

        String token = authHeader.substring(7);
        String userAccount;
        try {
            userAccount = JwtUtil.parseTokenForAccount(token);
        } catch (Exception e) {
            return ResultUtils.error(401, "token无效或过期");
        }

        User user = repo.findByUserAccount(userAccount);
        if (user == null) {
            return ResultUtils.error(404, "用户不存在");
        }

        return ResultUtils.success(user);
    }


    @Data
    static class AuthRequest {
        public String userAccount;     // ✅ 改字段名
        public String userPassword;    // ✅ 改字段名
    }
}
