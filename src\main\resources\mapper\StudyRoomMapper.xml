<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.StudyRoomMapper">

    <!-- 插入自习室 -->
    <insert id="insert" parameterType="com.example.model.StudyRoom"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO studyroom (
            roomName, ownerId, isPublic, maxMembers, imageIndex,
            tag, canUseTools, createTime, updateTime
        ) VALUES (
                     #{roomName}, #{ownerId}, #{isPublic}, #{maxMembers}, #{imageIndex},
                     #{tag}, #{canUseTools}, NOW(), NOW()
                 )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="long" resultType="com.example.model.StudyRoom">
        SELECT * FROM studyroom WHERE id = #{id}
    </select>

    <!-- 查询我创建的自习室 -->
    <select id="selectByOwnerId" parameterType="long" resultType="com.example.model.StudyRoom">
        SELECT * FROM studyroom WHERE ownerId = #{ownerId}
    </select>

    <!-- 查询所有公开房间 -->
    <select id="selectPublicRooms" resultType="com.example.model.StudyRoom">
        SELECT * FROM studyroom WHERE isPublic = true ORDER BY createTime DESC
    </select>

    <!-- 按标签筛选公开房间 -->
    <select id="selectPublicRoomsByTag" parameterType="string" resultType="com.example.model.StudyRoom">
        SELECT * FROM studyroom WHERE isPublic = true AND tag = #{tag} ORDER BY createTime DESC
    </select>

    <!-- 更新房间信息 -->
    <update id="updateRoom" parameterType="com.example.model.StudyRoom">
        UPDATE studyroom
        SET
            roomName = #{roomName},
            isPublic = #{isPublic},
            maxMembers = #{maxMembers},
            imageIndex = #{imageIndex},
            tag = #{tag},
            canUseTools = #{canUseTools},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除房间 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM studyroom WHERE id = #{id}
    </delete>

</mapper>
