package com.example.service.impl;

import com.example.dto.CommunityPostQueryRequest;
import com.example.model.CommunityPostVO;
import com.example.mapper.CommunityPostMapper;
import com.example.service.CommunityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CommunityServiceImpl implements CommunityService {

    @Autowired
    private CommunityPostMapper communityPostMapper;

    @Override
    public List<CommunityPostVO> listCommunityPosts(CommunityPostQueryRequest request) {
        int offset = (request.getPage() - 1) * request.getPageSize();
        return communityPostMapper.selectPostList(
                request.getTag(), request.getSortBy(), offset, request.getPageSize()
        );
    }
}
