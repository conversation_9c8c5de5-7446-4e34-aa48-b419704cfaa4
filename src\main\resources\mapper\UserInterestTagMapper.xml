<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.UserInterestTagMapper">

    <insert id="insertBatch">
        INSERT INTO userInterestTag (userId, tagId)
        VALUES
        <foreach collection="tagIds" item="tagId" separator=",">
            (#{userId}, #{tagId})
        </foreach>
    </insert>

    <insert id="insert" parameterType="com.example.model.UserInterestTag">
        INSERT INTO userInterestTag (userId, tagId)
        VALUES (#{userId}, #{tagId})
    </insert>

    <select id="getTagIdsByUserId" resultType="java.lang.Long">
        SELECT tagId FROM userinteresttag WHERE userId = #{userId}
    </select>

    <delete id="deleteByUserId">
        DELETE FROM userInterestTag WHERE userId = #{userId}
    </delete>

</mapper>
