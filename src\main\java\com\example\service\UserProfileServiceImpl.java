package com.example.service;

import com.example.dto.SaveTagRequest;
import com.example.dto.UserProfileAiResponse;
import com.example.mapper.UserInterestTagMapper;
import com.example.mapper.UserProfileAiMapper;
import com.example.model.UserInterestTag;
import com.example.model.UserProfileAi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private UserInterestTagMapper userInterestTagMapper;

    @Autowired
    private UserProfileAiMapper userProfileAiMapper;


    @Override
    public void saveUserTag(SaveTagRequest request) {
        Long userId = request.getUserId();
        List<Long> tagIds = request.getTagIdList();
        System.out.println("userId = " + userId);
        System.out.println("tagIds = " + tagIds);
        System.out.println("request = " + request);


        if (userId == null || tagIds == null || tagIds.isEmpty()) {
            throw new IllegalArgumentException("用户ID或标签ID列表为空");
        }

        userInterestTagMapper.deleteByUserId(userId);

        for (Long tagId : tagIds) {
            UserInterestTag entity = new UserInterestTag();
            entity.setUserId(userId);
            entity.setTagId(tagId);
            userInterestTagMapper.insert(entity);
        }
    }




    @Override
    public UserProfileAiResponse generateFakeAiProfile(Long userId) {
        // 查询用户绑定的兴趣标签
        List<Long> tagIds = userInterestTagMapper.getTagIdsByUserId(userId);
        String joinedTagIds = tagIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        // 构造新的 UserProfileAi 实体
        UserProfileAi profile = new UserProfileAi();
        profile.setUserId(userId);
        profile.setDominantTopic("人工智能与数学");
        profile.setPreferredContentTagIds(joinedTagIds);
        profile.setLearningStyle("visual");
        profile.setCareerTendency("技术研究员");
        profile.setCognitiveStrengths("逻辑推理、抽象思维");
        profile.setStudyRhythm("evening");
        profile.setPlanTypePreference("flexible");
        profile.setWeakKnowledgePoints("{\"线性代数\": \"掌握较弱\"}");
        profile.setResourceRecommendationMode("ai_generated");

        // 更新或插入
        UserProfileAi old = userProfileAiMapper.selectByUserId(userId);
        if (old != null) {
            profile.setId(old.getId());
        }
        userProfileAiMapper.insertOrUpdate(profile); // ✅ 使用已有方法，避免 updateById 错误


        return convertToResponse(profile);

    }

    @Override
    public UserProfileAiResponse getUserProfile(Long userId) {
        UserProfileAi profile = userProfileAiMapper.selectByUserId(userId);
        return convertToResponse(profile);
    }

    private UserProfileAiResponse convertToResponse(UserProfileAi profile) {
        UserProfileAiResponse resp = new UserProfileAiResponse();
        resp.setUserId(profile.getUserId());
        resp.setDominantTopic(profile.getDominantTopic());
        resp.setPreferredContentTagIds(profile.getPreferredContentTagIds());
        resp.setLearningStyle(profile.getLearningStyle());
        resp.setCareerTendency(profile.getCareerTendency());
        resp.setCognitiveStrengths(profile.getCognitiveStrengths());
        resp.setStudyRhythm(profile.getStudyRhythm());
        resp.setPlanTypePreference(profile.getPlanTypePreference());
        resp.setWeakKnowledgePoints(profile.getWeakKnowledgePoints());
        resp.setResourceRecommendationMode(profile.getResourceRecommendationMode());
        return resp;
    }
}
