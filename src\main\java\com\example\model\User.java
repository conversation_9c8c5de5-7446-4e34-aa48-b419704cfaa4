package com.example.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    private Long id;
    private String userAccount;
    private String userPassword;
    private String userName;    // 用户名 / 昵称
    private String userAvatar;  // 头像 URL
    private String userProfile; // 个人简介
    private String userRole;    // 角色（user / admin）
    private Date createTime;
    private Date updateTime;
    private Integer isCompleted;  // 是否完善资料（0/1）
}
