package com.example.utils;

public class ResultUtils {
    public static <T> BaseResponse<T> success(T data) {
        BaseResponse<T> result = new BaseResponse<>();
        result.setCode(0);
        result.setMessage("ok");
        result.setData(data);
        return result;
    }

    public static <T> BaseResponse<T> error(int code, String message) {
        BaseResponse<T> result = new BaseResponse<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(null);
        return result;
    }
}
