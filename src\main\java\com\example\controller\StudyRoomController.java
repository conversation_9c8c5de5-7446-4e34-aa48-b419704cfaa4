package com.example.controller;

import com.example.dto.CreateRoomRequest;
import com.example.mapper.UserMapper;
import com.example.model.User;
import com.example.service.StudyRoomService;
import com.example.utils.BaseResponse;
import com.example.utils.ResultUtils;
import com.example.utils.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/studyRoom")
public class StudyRoomController {

    @Autowired
    private StudyRoomService studyRoomService;

    @Autowired
    private UserMapper userMapper; // ✅ 注入用于查询 userId

    @PostMapping("/create")
    public BaseResponse<Boolean> createRoom(@RequestBody CreateRoomRequest request,
                                            HttpServletRequest httpRequest) {
        String userAccount = JwtUtil.getUserAccountFromRequest(httpRequest); // ✅ 从 token 中解析
        User user = userMapper.findByUserAccount(userAccount); // ✅ 查库获取 userId
        if (user == null) {
            return ResultUtils.error(401, "用户不存在");
        }

        Long userId = user.getId();
        boolean success = studyRoomService.createRoom(userId, request);
        return ResultUtils.success(success);
    }
}
