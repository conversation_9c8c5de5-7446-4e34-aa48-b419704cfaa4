package com.example.controller;

import com.example.mapper.UserMapper;
import com.example.model.User;
import com.example.service.CommunityLikeService;
import com.example.utils.BaseResponse;
import com.example.utils.JwtUtil;
import com.example.utils.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/community/like")
public class CommunityLikeController {

    @Autowired
    private CommunityLikeService likeService;

    @Autowired
    private UserMapper userMapper;

    @PostMapping("/{postId}")
    public BaseResponse<Boolean> like(@PathVariable Long postId, HttpServletRequest request) {
        String userAccount = JwtUtil.getUserAccountFromRequest(request);
        User user = userMapper.findByUserAccount(userAccount);
        if (user == null) return ResultUtils.error(401, "未登录");

        boolean success = likeService.likePost(postId, user.getId());
        return ResultUtils.success(success);
    }

    @DeleteMapping("/{postId}")
    public BaseResponse<Boolean> cancelLike(@PathVariable Long postId, HttpServletRequest request) {
        String userAccount = JwtUtil.getUserAccountFromRequest(request);
        User user = userMapper.findByUserAccount(userAccount);
        if (user == null) return ResultUtils.error(401, "未登录");

        boolean success = likeService.cancelLike(postId, user.getId());
        return ResultUtils.success(success);
    }
}