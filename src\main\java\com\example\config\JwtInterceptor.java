package com.example.config;

import com.example.utils.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

public class JwtInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("Authorization");

        if (token == null || token.isEmpty()) {
            response.setStatus(401);
            response.getWriter().write("未登录或Token缺失");
            return false;
        }

        try {
            // ✅ 处理 Bearer 前缀
            if (token.startsWith("Bearer")) {
                token = token.substring(7); // 去掉 "Bearer " 前缀
            }

            // ✅ 验证 token
            JwtUtil.validateToken(token);

            return true; // 验证成功，放行请求
        } catch (Exception e) {
            // ✅ 控制台打印错误信息
            System.out.println("【JWT 验证失败】" + e.getMessage());
            e.printStackTrace();

            response.setStatus(401);
            response.getWriter().write("无效Token，请重新登录");
            return false;
        }
    }
}
