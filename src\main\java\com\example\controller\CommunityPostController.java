package com.example.controller;

import com.example.dto.CreatePostRequest;
import com.example.mapper.UserMapper;
import com.example.model.User;
import com.example.service.CommunityPostService;
import java.util.List;
import java.util.Map;
import com.example.utils.BaseResponse;
import com.example.utils.JwtUtil;
import com.example.utils.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/community/post")
public class CommunityPostController {

    @Autowired
    private CommunityPostService communityPostService;

    @Autowired
    private UserMapper userMapper;

    @PostMapping("/create")
    public BaseResponse<Long> createPost(@RequestBody CreatePostRequest request, HttpServletRequest httpRequest) {
        String userAccount = JwtUtil.getUserAccountFromRequest(httpRequest);
        User user = userMapper.findByUserAccount(userAccount);
        if (user == null) {
            throw new RuntimeException("用户不存在！");
        }
        Long userId = user.getId();
        Long postId = communityPostService.createPost(request, userId);
        return ResultUtils.success(postId);
    }

    @GetMapping("/list")
    public BaseResponse<List<Map<String, Object>>> listPostsByTag(
            @RequestParam(value = "tag", required = false) String tag,
            @RequestParam(value = "sort", required = false, defaultValue = "time") String sort
    ) {
        List<Map<String, Object>> postList = communityPostService.listByTagAndSort(tag, sort);
        return ResultUtils.success(postList);
    }




}
