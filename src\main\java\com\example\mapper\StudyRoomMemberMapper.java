package com.example.mapper;

import com.example.model.StudyRoomMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StudyRoomMemberMapper {

    // 插入成员（用户加入房间）
    int insert(StudyRoomMember member);

    // 查询某个房间的所有成员
    List<StudyRoomMember> selectMembersByRoomId(@Param("roomId") Long roomId);

    // 查询用户加入的所有房间
    List<StudyRoomMember> selectRoomsByUserId(@Param("userId") Long userId);

    // 查询用户是否已经加入某房间
    StudyRoomMember selectByRoomIdAndUserId(@Param("roomId") Long roomId, @Param("userId") Long userId);

    // 将成员状态设为退出
    int markExit(@Param("roomId") Long roomId, @Param("userId") Long userId);

    // 删除成员记录（可选：房主解散、成员退出）
    int deleteByRoomIdAndUserId(@Param("roomId") Long roomId, @Param("userId") Long userId);
}
