package com.example.controller;

import com.example.dto.HomePageResponse;
import com.example.mapper.UserMapper;
import com.example.model.User;
import com.example.service.HomeService;
import com.example.utils.BaseResponse;
import com.example.utils.JwtUtil;
import com.example.utils.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/index")
public class HomeController {

    @Autowired
    private HomeService homeService;

    @Autowired
    private UserMapper userMapper; // ✅ 注入 UserMapper，用于查 userId

    @GetMapping
    public BaseResponse<HomePageResponse> getHomePageContent(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        Long userId = null;

        // 支持带 Token 的访问（登录用户）
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            try {
                String userAccount = JwtUtil.parseTokenForAccount(token); // ✅ 从 token 中获取 userAccount
                if (userAccount != null) {
                    User user = userMapper.findByUserAccount(userAccount); // ✅ 再查 userId
                    if (user != null) {
                        userId = user.getId();
                        System.out.println("✅ 已登录用户，userId = " + userId);
                    }
                }
            } catch (Exception e) {
                System.out.println("❌ Token 解析异常：" + e.getMessage());
            }
        } else {
            System.out.println("⚠️ 未携带 Authorization，走游客推荐逻辑");
        }

        HomePageResponse response = homeService.getHomePageContent(userId);
        return ResultUtils.success(response);
    }
}
