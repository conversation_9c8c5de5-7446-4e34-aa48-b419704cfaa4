package com.example.mapper;

import com.example.model.UserInterestTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface UserInterestTagMapper {

    // 批量插入
    void insertBatch(@Param("userId") Long userId, @Param("tagIds") List<Long> tagIds);

    // 根据用户 ID 删除旧记录（用于更新）
    void deleteByUserId(@Param("userId") Long userId);

    int insert(UserInterestTag entity);

    List<Long> getTagIdsByUserId(Long userId);


}
