
package com.example.dto;

import com.example.model.ContentVO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class HomePageResponse {
    private List<ContentVO> featuredContentList;
    private List<ContentVO> hotCourseList;
    private List<ContentVO> latestCourseList;
    private List<ContentVO> recommendVideoList;
    private List<ContentVO> interestRecommend;
    private List<ContentVO> profileRecommend; // 新增：基于其他画像字段推荐的内容



    public HomePageResponse() {

    }
}
