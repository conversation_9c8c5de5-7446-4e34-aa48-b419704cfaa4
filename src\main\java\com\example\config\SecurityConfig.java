package com.example.config;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                .cors()  // ✅ 开启 CORS
                .and()
                .csrf().disable() // ✅ 禁用 CSRF（前后端分离通常禁用）
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers("/api/auth/**").permitAll() // 登录、注册
                        .requestMatchers("/api/index/**").permitAll() // 首页推荐
                        .requestMatchers("/api/profile/**").permitAll() // 游客用户画像获取等
                        .requestMatchers("/images/**").permitAll() // 静态资源
                        .requestMatchers(org.springframework.http.HttpMethod.OPTIONS, "/**").permitAll() // 跨域预检请求
                        .anyRequest().permitAll() // 其他默认放行（可改为 authenticated）
                )

                .exceptionHandling(e -> e
                        .authenticationEntryPoint((req, res, ex) -> res.sendError(HttpServletResponse.SC_UNAUTHORIZED, "未登录"))
                );
        return http.build();
    }
}
