<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.CommunityPostMapper">

    <insert id="insertPost" parameterType="com.example.model.CommunityPost" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO communityPost (
            userId, title, content, imageUrls, tag,
            likeCount, commentCount, viewCount, isHidden, createTime, updateTime
        ) VALUES (
                     #{userId}, #{title}, #{content}, #{imageUrls}, #{tag},
                     #{likeCount}, #{commentCount}, #{viewCount}, #{isHidden}, #{createTime}, #{updateTime}
                 )
    </insert>
    <update id="increaseLikeCount">
        UPDATE communityPost SET likeCount = likeCount + 1 WHERE id = #{postId}
    </update>

    <update id="decreaseLikeCount">
        UPDATE communityPost SET likeCount = likeCount - 1 WHERE id = #{postId} AND likeCount > 0
    </update>
    <select id="selectByTagAndSort" resultType="map">
        SELECT
        p.id, p.userId, p.title, p.content, p.imageUrls, p.tag,
        p.likeCount, p.commentCount, p.viewCount, p.createTime,
        u.userName, u.userAvatar
        FROM communitypost p
        LEFT JOIN user u ON p.userId = u.id
        <where>
            p.isHidden = 0
            <if test="tag != null and tag != ''">
                AND p.tag = #{tag}
            </if>
        </where>
        <choose>
            <when test="sort == 'like'">
                ORDER BY p.likeCount DESC
            </when>
            <otherwise>
                ORDER BY p.createTime DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectPostList" resultType="com.example.model.CommunityPostVO">
        SELECT
        p.id, p.userId, p.title, p.content, p.imageUrls, p.tag,
        p.likeCount, p.commentCount, p.viewCount, p.createTime,
        u.userName, u.userAvatar
        FROM communitypost p
        LEFT JOIN user u ON p.userId = u.id
        <where>
            p.isHidden = 0
            <if test="tag != null and tag != ''">
                AND p.tag = #{tag}
            </if>
        </where>
        <choose>
            <when test="sortBy == 'like'">
                ORDER BY p.likeCount DESC
            </when>
            <otherwise>
                ORDER BY p.createTime DESC
            </otherwise>
        </choose>
        LIMIT #{offset}, #{pageSize}
    </select>




</mapper>