package com.example.service;

import com.example.mapper.CommunityLikeMapper;
import com.example.mapper.CommunityPostMapper;
import com.example.model.CommunityLike;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class CommunityLikeServiceImpl implements CommunityLikeService {

    @Autowired
    private CommunityLikeMapper likeMapper;

    @Autowired
    private CommunityPostMapper postMapper;

    @Override
    public boolean likePost(Long postId, Long userId) {
        boolean exists = likeMapper.exists(postId, userId);
        if (exists) return false;

        CommunityLike like = new CommunityLike();
        like.setPostId(postId);
        like.setUserId(userId);
        like.setCreateTime(new Date());
        likeMapper.insertLike(like);

        postMapper.increaseLikeCount(postId); // 自定义 SQL 需要实现
        return true;
    }

    @Override
    public boolean cancelLike(Long postId, Long userId) {
        int rows = likeMapper.deleteLike(postId, userId);
        if (rows > 0) {
            postMapper.decreaseLikeCount(postId); // 自定义 SQL 需要实现
            return true;
        }
        return false;
    }
}