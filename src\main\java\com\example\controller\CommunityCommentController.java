package com.example.controller;

import com.example.dto.CreateCommentRequest;
import com.example.model.CommentVO;
import com.example.model.User;
import com.example.service.CommunityCommentService;
import com.example.mapper.UserMapper;
import com.example.utils.BaseResponse;
import com.example.utils.JwtUtil;
import com.example.utils.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/community/comment")
public class CommunityCommentController {

    @Autowired
    private CommunityCommentService commentService;

    @Autowired
    private UserMapper userMapper;

    @PostMapping
    public BaseResponse<Boolean> addComment(@RequestBody CreateCommentRequest request, HttpServletRequest httpRequest) {
        String userAccount = JwtUtil.getUserAccountFromRequest(httpRequest);
        User user = userMapper.findByUserAccount(userAccount);
        if (user == null) {
            return ResultUtils.error(401, "用户未登录");
        }
        commentService.addComment(request, user.getId());
        return ResultUtils.success(true);
    }

    @GetMapping("/{postId}")
    public BaseResponse<List<CommentVO>> getComments(@PathVariable Long postId) {
        List<CommentVO> comments = commentService.getCommentsByPostId(postId);
        return ResultUtils.success(comments);
    }
}
