package com.example.mapper;

import com.example.model.CommunityLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CommunityLikeMapper {
    int insertLike(CommunityLike like);
    int deleteLike(@Param("postId") Long postId, @Param("userId") Long userId);
    boolean exists(@Param("postId") Long postId, @Param("userId") Long userId);
}