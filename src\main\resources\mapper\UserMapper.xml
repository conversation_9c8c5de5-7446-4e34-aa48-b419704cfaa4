<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">

    <!-- 登录：根据账号查询用户 -->
    <select id="findByUserAccount" resultType="com.example.model.User">
        SELECT * FROM user WHERE userAccount = #{userAccount}
    </select>

    <!-- 注册：插入用户（假设有 user_account 和 password 字段） -->
    <insert id="insert" parameterType="com.example.model.User">
        INSERT INTO user (userAccount, userPassword)
        VALUES (#{userAccount}, #{userPassword})
    </insert>

    <select id="findById" resultType="com.example.model.User">
        SELECT * FROM user WHERE id = #{id}
    </select>
    <select id="getIdByUserAccount" resultType="long">
        SELECT id FROM user WHERE userAccount = #{userAccount}
    </select>


</mapper>
