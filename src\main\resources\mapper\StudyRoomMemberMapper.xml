<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.StudyRoomMemberMapper">

    <!-- 插入成员记录 -->
    <insert id="insert" parameterType="com.example.model.StudyRoomMember">
        INSERT INTO studyroommember (roomId, userId, joinTime, status)
        VALUES (#{roomId}, #{userId}, NOW(), #{status})
    </insert>

    <!-- 查询房间所有成员 -->
    <select id="selectMembersByRoomId" parameterType="long" resultType="com.example.model.StudyRoomMember">
        SELECT * FROM studyroommember
        WHERE roomId = #{roomId} AND status = 1
    </select>

    <!-- 查询用户加入的所有房间 -->
    <select id="selectRoomsByUserId" parameterType="long" resultType="com.example.model.StudyRoomMember">
        SELECT * FROM studyroommember
        WHERE userId = #{userId} AND status = 1
    </select>

    <!-- 查询用户是否在某房间内 -->
    <select id="selectByRoomIdAndUserId" resultType="com.example.model.StudyRoomMember">
        SELECT * FROM studyroommember
        WHERE roomId = #{roomId} AND userId = #{userId}
    </select>

    <!-- 更新为已退出状态 -->
    <update id="markExit">
        UPDATE studyroommember
        SET status = 0
        WHERE roomId = #{roomId} AND userId = #{userId}
    </update>

    <!-- 删除记录 -->
    <delete id="deleteByRoomIdAndUserId">
        DELETE FROM studyroommember
        WHERE roomId = #{roomId} AND userId = #{userId}
    </delete>

</mapper>
