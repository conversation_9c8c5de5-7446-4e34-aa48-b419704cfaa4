<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.ContentMapper">

    <!-- 精品课程 -->
    <select id="selectFeatured" resultType="com.example.model.ContentVO">
        SELECT c.id, c.contentTitle, c.contentType, c.coverUrl, c.sourceUniversity,
               cs.playCount, c.isFeatured,
               (SELECT COUNT(*) FROM contentcomment cm WHERE cm.contentId = c.id) AS commentCount
        FROM content c
                 LEFT JOIN contentStat cs ON c.id = cs.contentId
        WHERE c.isFeatured = TRUE
        ORDER BY c.createTime DESC
            LIMIT 5
    </select>

    <!-- 热门课程排行榜 -->
    <select id="selectHotRanking" resultType="com.example.model.ContentVO">
        SELECT c.id, c.contentTitle, c.contentType, c.coverUrl, c.sourceUniversity,
               cs.playCount, c.isFeatured,
               (SELECT COUNT(*) FROM contentcomment cm WHERE cm.contentId = c.id) AS commentCount
        FROM content c
                 LEFT JOIN contentStat cs ON c.id = cs.contentId
        WHERE c.contentType = 'course'
        ORDER BY cs.playCount DESC
            LIMIT 10
    </select>

    <!-- 推荐视频（兴趣驱动） -->
    <select id="selectRecommendVideo" resultType="com.example.model.ContentVO">
        SELECT DISTINCT c.id, c.contentTitle, c.contentType, c.coverUrl, c.sourceUniversity,
                        cs.playCount, c.isFeatured,
                        (SELECT COUNT(*) FROM contentcomment cm WHERE cm.contentId = c.id) AS commentCount
        FROM content c
                 LEFT JOIN contentStat cs ON c.id = cs.contentId
                 JOIN contentTag ct ON c.id = ct.contentId
                 JOIN userProfileAi p ON FIND_IN_SET(ct.tagId, p.preferredContentTagIds)
        WHERE c.contentType = 'video' AND p.userId = #{userId}
        ORDER BY cs.playCount DESC
            LIMIT 10
    </select>

    <!-- 最新视频（游客默认推荐） -->
    <select id="selectLatestVideo" resultType="com.example.model.ContentVO">
        SELECT c.id, c.contentTitle, c.contentType, c.coverUrl, c.sourceUniversity,
               cs.playCount, c.isFeatured,
               (SELECT COUNT(*) FROM contentcomment cm WHERE cm.contentId = c.id) AS commentCount
        FROM content c
                 LEFT JOIN contentStat cs ON c.id = cs.contentId
        WHERE c.contentType = 'video'
        ORDER BY c.createTime DESC
            LIMIT 10
    </select>

    <!-- 最新课程 -->
    <select id="selectLatestCourse" resultType="com.example.model.ContentVO">
        SELECT c.id, c.contentTitle, c.contentType, c.coverUrl, c.sourceUniversity,
               cs.playCount, c.isFeatured,
               (SELECT COUNT(*) FROM contentcomment cm WHERE cm.contentId = c.id) AS commentCount
        FROM content c
                 LEFT JOIN contentStat cs ON c.id = cs.contentId
        WHERE c.contentType = 'course'
        ORDER BY c.createTime DESC
            LIMIT 10
    </select>

    <!-- 推荐内容：根据标签 ID 列表 -->
    <select id="selectContentByTagIds" resultType="com.example.model.ContentVO">
        SELECT c.*
        FROM content c
        JOIN contenttag ct ON c.id = ct.contentId
        WHERE ct.tagId IN
        <foreach collection="list" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>

    <!-- 根据画像字段多维匹配推荐 -->
    <select id="selectContentByProfileAttributes" resultType="com.example.model.ContentVO">
        SELECT c.*
        FROM content c
                 LEFT JOIN contentattribute ca1 ON c.id = ca1.contentId AND ca1.attributeType = 'dominantTopic'
                 LEFT JOIN contentattribute ca2 ON c.id = ca2.contentId AND ca2.attributeType = 'learningStyle'
                 LEFT JOIN contentattribute ca3 ON c.id = ca3.contentId AND ca3.attributeType = 'careerTendency'
        WHERE
            (ca1.attributeValue = #{topic} OR #{topic} IS NULL)
           OR (ca2.attributeValue = #{style} OR #{style} IS NULL)
           OR (ca3.attributeValue = #{career} OR #{career} IS NULL)
    </select>


</mapper>
