package com.example.model;

import lombok.Data;

import java.util.Date;

@Data
public class CommunityPost {
    private Long id;
    private Long userId;
    private String title;
    private String content;
    private String imageUrls;
    private String tag;
    private Integer likeCount;
    private Integer commentCount;
    private Integer viewCount;
    private Boolean isHidden;
    private Date createTime;
    private Date updateTime;
    private String userName;
    private String userAvatar;
}

