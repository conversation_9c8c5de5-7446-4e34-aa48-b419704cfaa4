<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.CommunityCommentMapper">

    <insert id="insertComment" parameterType="com.example.model.CommunityComment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO communityComment (postId, userId, commentContent, createTime)
        VALUES (#{postId}, #{userId}, #{commentContent}, #{createTime})
    </insert>

    <select id="selectCommentsByPostId" resultType="com.example.model.CommentVO">
        SELECT c.id, c.userId, u.userName, c.commentContent, c.createTime
        FROM communityComment c
                 JOIN user u ON c.userId = u.id
        WHERE c.postId = #{postId}
        ORDER BY c.createTime DESC
    </select>

</mapper>