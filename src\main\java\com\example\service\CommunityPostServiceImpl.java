package com.example.service;

import com.example.dto.CreatePostRequest;
import com.example.mapper.CommunityPostMapper;
import com.example.model.CommunityPost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Date;

@Service
public class CommunityPostServiceImpl implements CommunityPostService {

    @Autowired
    private CommunityPostMapper communityPostMapper;
    @Override
    public List<Map<String, Object>> listByTagAndSort(String tag, String sort) {
        return communityPostMapper.selectByTagAndSort(tag, sort);
    }



    @Override
    public Long createPost(CreatePostRequest request, Long userId) {
        CommunityPost post = new CommunityPost();
        post.setUserId(userId);
        post.setTitle(request.getTitle());
        post.setContent(request.getContent());
        post.setImageUrls(request.getImageUrls());
        post.setTag(request.getTag());
        post.setLikeCount(0);
        post.setCommentCount(0);
        post.setViewCount(0);
        post.setIsHidden(false);
        post.setCreateTime(new Date());
        post.setUpdateTime(new Date());
        communityPostMapper.insertPost(post);
        return post.getId();
    }
}