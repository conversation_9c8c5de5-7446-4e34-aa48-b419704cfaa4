package com.example.service;

import com.example.dto.CreateCommentRequest;
import com.example.mapper.CommunityCommentMapper;
import com.example.model.CommunityComment;
import com.example.model.CommentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CommunityCommentServiceImpl implements CommunityCommentService {

    @Autowired
    private CommunityCommentMapper commentMapper;

    @Override
    public void addComment(CreateCommentRequest request, Long userId) {
        CommunityComment comment = new CommunityComment();
        comment.setPostId(request.getPostId());
        comment.setUserId(userId);
        comment.setCommentContent(request.getCommentContent());
        comment.setCreateTime(new Date());
        commentMapper.insertComment(comment);
    }

    @Override
    public List<CommentVO> getCommentsByPostId(Long postId) {
        return commentMapper.selectCommentsByPostId(postId);
    }
}