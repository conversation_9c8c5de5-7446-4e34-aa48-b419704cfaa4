package com.example.mapper;

import com.example.model.CommunityPost;
import com.example.model.CommunityPostVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface CommunityPostMapper {
    int insertPost(CommunityPost post);
    int increaseLikeCount(@Param("postId") Long postId);
// 减少点赞数量
    int decreaseLikeCount(@Param("postId") Long postId);

    List<Map<String, Object>> selectByTagAndSort(@Param("tag") String tag, @Param("sort") String sort);
    List<CommunityPostVO> selectPostList(@Param("tag") String tag,
                                         @Param("sortBy") String sortBy,
                                         @Param("offset") int offset,
                                         @Param("pageSize") int pageSize);


}