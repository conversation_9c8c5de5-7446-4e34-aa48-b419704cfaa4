<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserProfileAiMapper">

    <insert id="insertOrUpdate">
        INSERT INTO userProfileAi(
            userId, dominantTopic, preferredContentTagIds, learningStyle,
            careerTendency, cognitiveStrengths, studyRhythm,
            planTypePreference, weakKnowledgePoints, resourceRecommendationMode
        ) VALUES (
                     #{profile.userId}, #{profile.dominantTopic}, #{profile.preferredContentTagIds},
                     #{profile.learningStyle}, #{profile.careerTendency}, #{profile.cognitiveStrengths},
                     #{profile.studyRhythm}, #{profile.planTypePreference}, #{profile.weakKnowledgePoints},
                     #{profile.resourceRecommendationMode}
                 )
            ON DUPLICATE KEY UPDATE
                                 dominantTopic = VALUES(dominantTopic),
                                 preferredContentTagIds = VALUES(preferredContentTagIds),
                                 learningStyle = VALUES(learningStyle),
                                 careerTendency = VALUES(careerTendency),
                                 cognitiveStrengths = VALUES(cognitiveStrengths),
                                 studyRhythm = VALUES(studyRhythm),
                                 planTypePreference = VALUES(planTypePreference),
                                 weakKnowledgePoints = VALUES(weakKnowledgePoints),
                                 resourceRecommendationMode = VALUES(resourceRecommendationMode),
                                 updateTime = NOW()
    </insert>

    <select id="selectByUserId" resultType="com.example.model.UserProfileAi">
        SELECT * FROM userProfileAi WHERE userId = #{userId}
    </select>

</mapper>